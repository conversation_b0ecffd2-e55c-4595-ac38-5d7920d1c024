import { AppTheme } from "@/src/constants/Colors";
import React, { createContext, useContext, useEffect, useState } from "react";
import { useColorScheme } from "react-native";
import { MMKV } from "react-native-mmkv";

import { ThemeContextType, ThemeType } from "@/src/types";

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const storage = new MMKV();

const THEME_KEY = "theme";

export function ThemeProviderContext({
  children,
}: {
  children: React.ReactNode;
}) {
  const systemColorScheme = useColorScheme() ?? "light";

  // Initialize theme from MMKV synchronously with error handling
  const [theme, setThemeState] = useState<ThemeType>(() => {
    try {
      const saved = storage.getString(THEME_KEY);
      if (saved === "light" || saved === "dark" || saved === "system") {
        return saved as ThemeType;
      }
    } catch (error) {
      console.warn("Failed to load theme from storage:", error);
    }
    return "system";
  });

  // Save theme changes to MMKV whenever theme changes
  useEffect(() => {
    try {
      storage.set(THEME_KEY, theme);
    } catch (error) {
      console.warn("Failed to save theme to storage:", error);
    }
  }, [theme]);

  const setTheme = (newTheme: ThemeType) => {
    setThemeState(newTheme);
  };

  const currentTheme: AppTheme =
    theme === "system"
      ? (systemColorScheme as AppTheme) || "dark"
      : (theme as AppTheme) || "dark";

  return (
    <ThemeContext.Provider value={{ theme, setTheme, currentTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    console.error("useTheme must be used within a ThemeProviderContext");
    // Return a default theme instead of throwing to prevent crashes
    return {
      theme: "system" as ThemeType,
      setTheme: () => {},
      currentTheme: "dark" as AppTheme,
    };
  }
  // Ensure currentTheme is always defined
  return {
    ...context,
    currentTheme: context.currentTheme || "dark",
  };
}
