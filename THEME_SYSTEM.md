# Theme System Documentation

## Overview

The app now supports a comprehensive theme system with 10 different themes (5 light + 5 dark) plus system theme detection. The theme system uses Expo SecureStore for persistence and automatically detects the system theme on first launch.

## Features

- **10 Themes**: 5 light themes and 5 dark themes in different colors
- **System Theme Detection**: Automatically chooses appropriate default theme based on system preference
- **Persistent Storage**: Uses Expo SecureStore to save user theme preferences
- **Fallback Support**: Graceful fallbacks if storage fails or theme is invalid

## Available Themes

### Light Themes
- `lightBlue` (default for light mode)
- `lightOrange`
- `lightRed`
- `lightPurple`
- `lightGreen`

### Dark Themes
- `darkBlue` (default for dark mode)
- `darkOrange`
- `darkRed`
- `darkPurple`
- `darkGreen`

### System Theme
- `system` - Automatically switches between `lightBlue` and `darkBlue` based on system preference

## Usage

### Using Theme Colors in Components

```tsx
import { useThemeColor, useCurrentThemeColors, useThemeColorDirect } from '@/hooks/useThemeColor';

// Method 1: With light/dark fallbacks (recommended for existing components)
const backgroundColor = useThemeColor({ light: '#fff', dark: '#000' }, 'background');

// Method 2: Get all colors from current theme
const colors = useCurrentThemeColors();
const backgroundColor = colors.background;

// Method 3: Get specific color directly
const primaryColor = useThemeColorDirect('primary');
```

### Using Linear Gradients

```tsx
import { useThemeLinearGradient } from '@/hooks/useThemeColor';

const gradientColors = useThemeLinearGradient(); // Returns [string, string]
```

### Changing Themes

```tsx
import { useTheme } from '@/context/ThemeContext';

function ThemeSelector() {
  const { theme, setTheme, currentTheme } = useTheme();
  
  return (
    <TouchableOpacity onPress={() => setTheme('darkPurple')}>
      <Text>Switch to Dark Purple</Text>
    </TouchableOpacity>
  );
}
```

## Theme Structure

Each theme contains the following colors:

```typescript
type ColorScheme = {
  primary: string;        // Main brand color
  text: string;          // Primary text color
  border: string;        // Border color (with opacity)
  secondary: string;     // Secondary background color (with opacity)
  background: string;    // Main background color
  icon: string;          // Icon color
  tabIconDefault: string; // Default tab icon color
  linearGradient: [string, string]; // Gradient colors
};
```

## Testing

A test screen is available at `/main/settings/theme-test` that allows you to:
- Switch between all 10 themes
- See real-time theme changes
- View color previews
- Test system theme detection

## Implementation Details

### Default Theme Selection
- On first app launch, the system detects the device's color scheme
- If system is light → defaults to `lightBlue`
- If system is dark → defaults to `darkBlue`
- User selections are saved to SecureStore and persist across app launches

### Storage
- Uses Expo SecureStore with key `user_theme_preference`
- Validates saved themes against allowed values
- Falls back to system-appropriate default if invalid theme is found

### Error Handling
- All SecureStore operations are wrapped in try-catch blocks
- Console warnings for storage failures (non-blocking)
- Graceful fallbacks ensure app never crashes due to theme issues

## Migration from Old System

The new system is backward compatible with existing `useThemeColor` usage:

```tsx
// This still works
const color = useThemeColor({ light: '#fff', dark: '#000' }, 'text');
```

The hook now:
1. Determines if current theme is light or dark
2. Uses the appropriate fallback color if provided
3. Otherwise uses the specific theme color

## Future Enhancements

- Theme preview in settings
- Custom theme creation
- Theme scheduling (e.g., auto-switch at sunset)
- Theme-based animations
