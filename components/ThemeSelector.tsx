import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { AppTheme } from '@/constants/Colors';
import { useCurrentThemeColors } from '@/hooks/useThemeColor';

const THEME_OPTIONS: { label: string; value: AppTheme | 'system' }[] = [
  { label: 'System Default', value: 'system' },
  { label: 'Light Orange', value: 'lightOrange' },
  { label: 'Light Red', value: 'lightRed' },
  { label: 'Light Purple', value: 'lightPurple' },
  { label: 'Light Blue', value: 'lightBlue' },
  { label: 'Light Green', value: 'lightGreen' },
  { label: 'Dark Orange', value: 'darkOrange' },
  { label: 'Dark Red', value: 'darkRed' },
  { label: 'Dark Purple', value: 'darkPurple' },
  { label: 'Dark Blue', value: 'darkBlue' },
  { label: 'Dark Green', value: 'darkGreen' },
];

export function ThemeSelector() {
  const { theme, setTheme, currentTheme } = useTheme();
  const colors = useCurrentThemeColors();

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Text style={[styles.title, { color: colors.text }]}>
        Choose Theme
      </Text>
      <Text style={[styles.subtitle, { color: colors.text }]}>
        Current: {currentTheme} (Selected: {theme})
      </Text>
      
      <ScrollView style={styles.scrollView}>
        {THEME_OPTIONS.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.themeOption,
              {
                backgroundColor: theme === option.value ? colors.primary : colors.secondary,
                borderColor: colors.border,
              },
            ]}
            onPress={() => setTheme(option.value)}
          >
            <Text
              style={[
                styles.themeOptionText,
                {
                  color: theme === option.value ? colors.background : colors.text,
                  fontWeight: theme === option.value ? 'bold' : 'normal',
                },
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <View style={[styles.previewContainer, { borderColor: colors.border }]}>
        <Text style={[styles.previewTitle, { color: colors.text }]}>
          Theme Preview
        </Text>
        <View style={styles.colorRow}>
          <View style={[styles.colorSwatch, { backgroundColor: colors.primary }]} />
          <Text style={[styles.colorLabel, { color: colors.text }]}>Primary</Text>
        </View>
        <View style={styles.colorRow}>
          <View style={[styles.colorSwatch, { backgroundColor: colors.secondary }]} />
          <Text style={[styles.colorLabel, { color: colors.text }]}>Secondary</Text>
        </View>
        <View style={styles.colorRow}>
          <View style={[styles.colorSwatch, { backgroundColor: colors.text }]} />
          <Text style={[styles.colorLabel, { color: colors.text }]}>Text</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    marginBottom: 20,
    opacity: 0.7,
  },
  scrollView: {
    flex: 1,
    marginBottom: 20,
  },
  themeOption: {
    padding: 15,
    marginVertical: 5,
    borderRadius: 8,
    borderWidth: 1,
  },
  themeOptionText: {
    fontSize: 16,
    textAlign: 'center',
  },
  previewContainer: {
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  colorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
  },
  colorSwatch: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 10,
  },
  colorLabel: {
    fontSize: 14,
  },
});
