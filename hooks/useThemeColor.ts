/**
 * Learn more about light and dark modes:
 * https://docs.expo.dev/guides/color-schemes/
 */
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

type ColorName = Exclude<keyof typeof Colors.lightBlue, 'linearGradient'>;

export function useThemeColor(props: { light?: string; dark?: string }, colorName: ColorName) {
  const { currentTheme } = useTheme();

  // Determine if current theme is light or dark for props fallback
  const isLightTheme = currentTheme.startsWith('light');
  const themeType = isLightTheme ? 'light' : 'dark';
  const colorFromProps = props[themeType];

  if (colorFromProps) {
    return colorFromProps;
  } else {
    // Use the specific theme colors
    return Colors[currentTheme][colorName];
  }
}

// New hook to get colors directly from current theme
export function useCurrentThemeColors() {
  const { currentTheme } = useTheme();
  return Colors[currentTheme];
}

// Helper hook to get a specific color from current theme
export function useThemeColorDirect(colorName: ColorName) {
  const { currentTheme } = useTheme();
  return Colors[currentTheme][colorName];
}

// Hook to get linear gradient colors from current theme
export function useThemeLinearGradient() {
  const { currentTheme } = useTheme();
  return Colors[currentTheme].linearGradient;
}
